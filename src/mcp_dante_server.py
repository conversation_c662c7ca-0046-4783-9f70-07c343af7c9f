#!/usr/bin/env python3
"""
MCP Server for Dante Earthquake API
Provides tools to interact with INGV's Dante earthquake data API
"""

import asyncio
import json
from typing import Any, Dict, List, Optional
from datetime import datetime

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

from dante_client import DanteAPIClient


# Global client instance
dante_client: Optional[DanteAPIClient] = None


def get_client() -> DanteAPIClient:
    """Get or create the Dante API client"""
    global dante_client
    if dante_client is None:
        dante_client = DanteAPIClient()
    return dante_client


# Create the MCP server
server = Server("dante-earthquake-api")


@server.list_tools()
async def handle_list_tools() -> List[Tool]:
    """List available tools for the Dante API"""
    return [
        Tool(
            name="dante_login",
            description="Authenticate with the Dante API to get access token",
            inputSchema={
                "type": "object",
                "properties": {
                    "name": {"type": "string", "description": "User name"},
                    "email": {"type": "string", "format": "email", "description": "User email"},
                    "password": {"type": "string", "description": "User password"}
                },
                "required": ["name", "email", "password"]
            }
        ),
        Tool(
            name="dante_status",
            description="Check the status of the Dante API",
            inputSchema={
                "type": "object",
                "properties": {},
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_events_group",
            description="Get clustered earthquake events with preferred origin and magnitude",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_events",
            description="Get earthquake events with preferred origin and magnitude from same instance",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_origins",
            description="Get earthquake origins data",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_magnitudes",
            description="Get earthquake magnitude data",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        ),
        Tool(
            name="dante_get_all_data",
            description="Get comprehensive earthquake data (origins with all magnitudes)",
            inputSchema={
                "type": "object",
                "properties": {
                    "starttime": {"type": "string", "description": "Start time (ISO format)"},
                    "endtime": {"type": "string", "description": "End time (ISO format)"},
                    "minlat": {"type": "number", "description": "Minimum latitude"},
                    "maxlat": {"type": "number", "description": "Maximum latitude"},
                    "minlon": {"type": "number", "description": "Minimum longitude"},
                    "maxlon": {"type": "number", "description": "Maximum longitude"},
                    "minmag": {"type": "number", "description": "Minimum magnitude"},
                    "maxmag": {"type": "number", "description": "Maximum magnitude"},
                    "mindepth": {"type": "number", "description": "Minimum depth (km)"},
                    "maxdepth": {"type": "number", "description": "Maximum depth (km)"},
                    "limit": {"type": "integer", "description": "Maximum number of results"},
                    "page": {"type": "integer", "description": "Page number for pagination"}
                },
                "additionalProperties": False
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
    """Handle tool calls for the Dante API"""
    
    try:
        client = get_client()
        
        if name == "dante_login":
            result = client.login(
                name=arguments["name"],
                email=arguments["email"],
                password=arguments["password"]
            )
            return [TextContent(
                type="text",
                text=f"Authentication successful. Token obtained: {result.get('token', 'N/A')[:20]}..."
            )]
        
        elif name == "dante_status":
            result = client.get_status()
            return [TextContent(
                type="text",
                text=f"API Status: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_events_group":
            # Filter out None values
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_events_group(**params)
            return [TextContent(
                type="text",
                text=f"Events Group Data: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_events":
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_events(**params)
            return [TextContent(
                type="text",
                text=f"Events Data: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_origins":
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_origins(**params)
            return [TextContent(
                type="text",
                text=f"Origins Data: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_magnitudes":
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_magnitudes(**params)
            return [TextContent(
                type="text",
                text=f"Magnitudes Data: {json.dumps(result, indent=2)}"
            )]
        
        elif name == "dante_get_all_data":
            params = {k: v for k, v in arguments.items() if v is not None}
            result = client.get_all_data(**params)
            return [TextContent(
                type="text",
                text=f"All Earthquake Data: {json.dumps(result, indent=2)}"
            )]
        
        else:
            return [TextContent(
                type="text",
                text=f"Unknown tool: {name}"
            )]
    
    except Exception as e:
        return [TextContent(
            type="text",
            text=f"Error calling {name}: {str(e)}"
        )]


async def main():
    """Main entry point for the MCP server"""
    async with stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="dante-earthquake-api",
                server_version="1.0.0",
                capabilities=server.get_capabilities(
                    notification_options=None,
                    experimental_capabilities=None,
                ),
            ),
        )


if __name__ == "__main__":
    asyncio.run(main())
