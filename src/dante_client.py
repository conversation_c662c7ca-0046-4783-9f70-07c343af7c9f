"""
Dante API Client for earthquake data from INGV
"""
import httpx
import logging
from typing import Optional, Dict, Any, List
from datetime import datetime
import json
import time


class DanteAPIClient:
    """Client for interacting with the Dante earthquake API"""

    def __init__(self, base_url: str = "https://caravel-dante.int.ingv.it/api"):
        self.base_url = base_url.rstrip('/')
        self.token: Optional[str] = None
        self.client = httpx.Client(timeout=30.0)

        # Set up logging
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers

    def _log_request(self, method: str, url: str, params: Optional[Dict] = None,
                    data: Optional[Dict] = None, start_time: float = None) -> None:
        """Log API request details"""
        duration = time.time() - start_time if start_time else 0

        log_data = {
            "method": method,
            "url": url,
            "duration_ms": round(duration * 1000, 2),
            "has_auth": bool(self.token)
        }

        if params:
            # Log only non-sensitive parameters
            safe_params = {k: v for k, v in params.items()
                          if k not in ['password', 'token']}
            log_data["params"] = safe_params

        if data:
            # Log only non-sensitive data
            safe_data = {k: v for k, v in data.items()
                        if k not in ['password', 'token']}
            log_data["data"] = safe_data

        self.logger.info(f"Dante API Request: {json.dumps(log_data, default=str)}")

    def _log_response(self, response: httpx.Response, start_time: float) -> None:
        """Log API response details"""
        duration = time.time() - start_time

        log_data = {
            "status_code": response.status_code,
            "duration_ms": round(duration * 1000, 2),
            "content_length": len(response.content) if response.content else 0,
            "url": str(response.url)
        }

        if response.status_code >= 400:
            self.logger.error(f"Dante API Error Response: {json.dumps(log_data)}")
            try:
                error_content = response.text[:500]  # First 500 chars of error
                self.logger.error(f"Error content: {error_content}")
            except:
                pass
        else:
            self.logger.info(f"Dante API Response: {json.dumps(log_data)}")

            # Log response summary for successful requests
            try:
                response_json = response.json()
                if isinstance(response_json, dict):
                    if 'data' in response_json:
                        data_count = len(response_json['data']) if isinstance(response_json['data'], list) else 1
                        self.logger.info(f"Response contains {data_count} data items")
                    if 'meta' in response_json:
                        meta = response_json['meta']
                        if 'total' in meta:
                            self.logger.info(f"Total available records: {meta['total']}")
            except:
                pass
    
    def login(self, name: str, email: str, password: str) -> Dict[str, Any]:
        """
        Authenticate with the Dante API

        Args:
            name: User name
            email: User email
            password: User password

        Returns:
            Authentication response with token
        """
        url = f"{self.base_url}/login"
        data = {
            "name": name,
            "email": email,
            "password": password
        }

        start_time = time.time()
        self.logger.warning("⚠️  LOGIN REQUEST: This is a POST request, not a GET request!")
        self._log_request("POST", url, data=data, start_time=start_time)

        response = self.client.post(url, json=data, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()

        result = response.json()
        if "token" in result:
            self.token = result["token"]
            self.logger.info("Authentication successful - token obtained")

        return result
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get API status

        Returns:
            Status information
        """
        url = f"{self.base_url}/status"
        start_time = time.time()
        self.logger.info("🔍 GET REQUEST: /status")
        self._log_request("GET", url, start_time=start_time)

        response = self.client.get(url, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
    
    def get_events_group(self, **params) -> Dict[str, Any]:
        """
        Get clustered events with preferred origin and magnitude

        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)

        Returns:
            Events group data
        """
        url = f"{self.base_url}/quakedb/v1/events-group"
        start_time = time.time()
        self.logger.info("🌍 GET REQUEST: /quakedb/v1/events-group")
        self._log_request("GET", url, params=params, start_time=start_time)

        response = self.client.get(url, params=params, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
    
    def get_events(self, **params) -> Dict[str, Any]:
        """
        Get events with preferred origin and magnitude from same instance

        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)

        Returns:
            Events data
        """
        url = f"{self.base_url}/quakedb/v1/events"
        start_time = time.time()
        self.logger.info("📍 GET REQUEST: /quakedb/v1/events")
        self._log_request("GET", url, params=params, start_time=start_time)

        response = self.client.get(url, params=params, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
    
    def get_origins(self, **params) -> Dict[str, Any]:
        """
        Get earthquake origins

        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)

        Returns:
            Origins data
        """
        url = f"{self.base_url}/quakedb/v1/origins"
        start_time = time.time()
        self.logger.info("🎯 GET REQUEST: /quakedb/v1/origins")
        self._log_request("GET", url, params=params, start_time=start_time)

        response = self.client.get(url, params=params, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
    
    def get_magnitudes(self, **params) -> Dict[str, Any]:
        """
        Get magnitude data

        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)

        Returns:
            Magnitudes data
        """
        url = f"{self.base_url}/quakedb/v1/magnitudes"
        start_time = time.time()
        self.logger.info("📊 GET REQUEST: /quakedb/v1/magnitudes")
        self._log_request("GET", url, params=params, start_time=start_time)

        response = self.client.get(url, params=params, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
    
    def get_all_data(self, **params) -> Dict[str, Any]:
        """
        Get all earthquake data (origins with all magnitudes)

        Args:
            **params: Query parameters (starttime, endtime, minlat, maxlat, etc.)

        Returns:
            All earthquake data
        """
        url = f"{self.base_url}/quakedb/v1/all"
        start_time = time.time()
        self.logger.info("📋 GET REQUEST: /quakedb/v1/all")
        self._log_request("GET", url, params=params, start_time=start_time)

        response = self.client.get(url, params=params, headers=self._get_headers())
        self._log_response(response, start_time)
        response.raise_for_status()
        return response.json()
