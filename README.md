# MCP Dante Server

A Model Context Protocol (MCP) server for interacting with INGV's Dante earthquake data API.

## Overview

This MCP server provides tools to interact with the Dante Web Services API from INGV (Istituto Nazionale di Geofisica e Vulcanologia), which provides access to earthquake data stored in the quakedb database.

## Features

The server provides the following tools:

- **dante_login**: Authenticate with the API to get access token
- **dante_status**: Check API status
- **dante_get_events_group**: Get clustered earthquake events with preferred origin and magnitude
- **dante_get_events**: Get earthquake events from the same instance
- **dante_get_origins**: Get earthquake origins data
- **dante_get_magnitudes**: Get earthquake magnitude data
- **dante_get_all_data**: Get comprehensive earthquake data

## Quick Start with Docker

### Prerequisites

- Docker and Docker Compose installed
- No authentication required for basic earthquake data queries

### Build and Run

1. Build the Docker container:
```bash
docker-compose build
```

2. Test the setup:
```bash
# Test API connectivity
docker-compose run --rm mcp-dante python src/test_dante_client.py

# Test MCP server
docker-compose run --rm mcp-dante python src/test_mcp_server.py

# Run example usage
docker-compose run --rm mcp-dante python src/example_usage.py
```

3. Run the MCP server:
```bash
docker-compose up mcp-dante
```

### Using the MCP Server

The server communicates via stdio (standard input/output) following the MCP protocol. You can interact with it using any MCP-compatible client.

#### Example Tool Calls

1. **Check API Status**:
```json
{
  "method": "tools/call",
  "params": {
    "name": "dante_status",
    "arguments": {}
  }
}
```

2. **Get Recent Earthquakes**:
```json
{
  "method": "tools/call",
  "params": {
    "name": "dante_get_events_group",
    "arguments": {
      "starttime": "2024-01-01T00:00:00Z",
      "endtime": "2024-01-31T23:59:59Z",
      "minmag": 3.0,
      "limit": 100
    }
  }
}
```

3. **Get Earthquakes in a Geographic Region**:
```json
{
  "method": "tools/call",
  "params": {
    "name": "dante_get_events",
    "arguments": {
      "minlat": 40.0,
      "maxlat": 45.0,
      "minlon": 10.0,
      "maxlon": 15.0,
      "minmag": 2.0,
      "limit": 50
    }
  }
}
```

## API Parameters

Common parameters supported by the earthquake data tools:

- **starttime/endtime**: Time range in ISO format (e.g., "2024-01-01T00:00:00Z")
- **minlat/maxlat**: Latitude range (-90 to 90)
- **minlon/maxlon**: Longitude range (-180 to 180)
- **minmag/maxmag**: Magnitude range
- **mindepth/maxdepth**: Depth range in kilometers
- **limit**: Maximum number of results
- **page**: Page number for pagination

## Development

### Project Structure

```
mcp-dante/
├── src/
│   ├── mcp_dante_server.py    # Main MCP server
│   └── dante_client.py        # API client wrapper
├── Dockerfile                 # Container definition
├── docker-compose.yml         # Container orchestration
├── requirements.txt           # Python dependencies
└── README.md                 # This file
```

### Local Development

If you want to run without Docker:

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the server:
```bash
python src/mcp_dante_server.py
```

## Authentication

If the API requires authentication, use the `dante_login` tool first:

```json
{
  "method": "tools/call",
  "params": {
    "name": "dante_login",
    "arguments": {
      "name": "Your Name",
      "email": "<EMAIL>",
      "password": "your_password"
    }
  }
}
```

## API Documentation

For detailed API documentation, refer to the Dante API OpenAPI specification:
https://caravel-dante.int.ingv.it/api/0.0.3/openapi.yaml

## License

This project follows the same license terms as the Dante API (Creative Commons Attribution 4.0 International).

## Contact

For issues related to the Dante API itself, contact: <EMAIL>
