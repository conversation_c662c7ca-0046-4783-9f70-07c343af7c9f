version: '3.8'

services:
  mcp-dante:
    build: .
    container_name: mcp-dante-server
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      # Mount source code for development (optional)
      - ./src:/app/src:ro
    stdin_open: true
    tty: true
    # For stdio communication with MCP
    command: ["python", "src/mcp_dante_server.py"]
    
  # Optional: Add a test service to interact with the MCP server
  mcp-dante-test:
    build: .
    container_name: mcp-dante-test
    environment:
      - PYTHONUNBUFFERED=1
      - PYTHONPATH=/app
    volumes:
      - ./src:/app/src:ro
      - ./tests:/app/tests:ro
    depends_on:
      - mcp-dante
    profiles:
      - test
    command: ["python", "-c", "print('Test container ready')"]
